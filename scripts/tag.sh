#!/bin/bash

# Function: Print the help message.
usage() {
	echo
	echo "Usage: t [-f] [-h] <tagname>"
	echo
	echo "tagname: the tag name, e.g. version."
	echo
	echo "Flags:"
	echo "  -f: Force to rewrite the tag."
	echo "  -h: Display the help message."
	echo
}


tag=$1

while getopts 'fh' flag
do
	case $flag in
		# Force to rewrite the tag.
		f)
			tag=$2
			git tag --delete $tag
			git push --delete origin $tag
			;;
		# Display the help message.
		h)
			usage
			exit 0
			;;
	esac
done

git add --all
git commit --message "Version $tag"
git tag $tag
git push && git push --tags
