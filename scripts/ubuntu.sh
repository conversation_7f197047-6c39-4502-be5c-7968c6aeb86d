#!/bin/bash

# LAMP
sudo apt install apache2 php7.3-fpm libapache2-mod-php php-mysql php-gd php-curl php-mbstring php-xml php-zip mariadb-server mariadb-client mailutils unzip
sudo a2enmod rewrite expires headers proxy proxy_fcgi setenvif

sudo apt install git vim nodejs npm composer curl
# sudo npm i -g node-sass postcss-cli autoprefixer

# wp-cli
curl -O https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar
chmod +x wp-cli.phar
sudo mv wp-cli.phar /usr/local/bin/wp

# Sublime Merge
wget -qO - https://download.sublimetext.com/sublimehq-pub.gpg | sudo apt-key add -
sudo apt-get install apt-transport-https
echo "deb https://download.sublimetext.com/ apt/stable/" | sudo tee /etc/apt/sources.list.d/sublime-text.list
sudo apt-get update
sudo apt-get install sublime-merge


# Setup SSH keys
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDELxBtxNhNTUPmRjUB6L/6z6D+uhOK02Fjeubm/X7oWWlKNtfNt/Mq0t3O8uDNFpN5V1YqB/qix7XoTkkBFNh3v2vgYag1xJMf78jLp3Mu2H0WzoBKGkIMfhoWLTG2NVHCHwL9Jz/9WE+jwLugevDoa1zC2QJ6VwzOi8yeNmv2HfA0LQRJHYGHRzWeNXqqudeXCBzRvdFKaPtzyVz+P3LiyjiP953PFtQNDs4L5lSyf70bwSaYRima5lKJ9Ar3DLksCPxoSl+TSOigH05xZJB80slC1XpjYo1tJn2zIz3S1cVsGJD/thButi5tXas8JjQMuNm+5oIfrznVhvsdODHv anhtran@DESKTOP-AHO3OAA" > ~/.ssh/id_rsa.pub
echo "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" > ~/.ssh/id_rsa
chmod 400 ~/.ssh/id_rsa
chmod 400 ~/.ssh/id_rsa.pub

# Setup from dotfiles
cd
<NAME_EMAIL>:anhtnt/dotfiles.git

ln -s ~/dotfiles/.vimrc ~/.vimrc
ln -s ~/dotfiles/.gitignore ~/.gitignore
ln -s ~/dotfiles/.gitattributes ~/.gitattributes
ln -s ~/dotfiles/.gitconfig ~/.gitconfig
ln -s ~/dotfiles/.ssh/config ~/.ssh/config

# Add to .bashrc
# source ~/dotfiles/.bashrc

# Install Vundle
git clone https://github.com/VundleVim/Vundle.vim.git ~/.vim/bundle/Vundle.vim
# Run at Vim's command line: `:PluginInstall`

# VIM: install colorscheme.
mkdir -p ~/.vim/colors &&  cd ~/.vim/colors && wget https://raw.githubusercontent.com/arcticicestudio/nord-vim/develop/colors/nord.vim