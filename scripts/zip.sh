#!/bin/bash

name=${PWD##*/}

echo "# Creating zip file..."

echo "  - Installing PHP dependencies"
composer install --no-dev

echo "  - Installing JS dependencies"
pnpm install

echo "  - Creating built JS files"
pnpm run build

echo "  - Copying files..."
mkdir -p $name
rm -rf $name/*
cp .distignore .distignore-production
echo $name >> .distignore-production
echo .distignore-production >> .distignore-production
rsync -rc --exclude-from=".distignore-production" . $name

echo "  - Generating zip file..."
zip -r "${name}.zip" $name

echo "  - Cleaning up..."
rm -rf $name
rm .distignore-production

echo -e "\nDONE\n"