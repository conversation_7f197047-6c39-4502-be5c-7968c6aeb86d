# Install Vundle
git clone https://github.com/VundleVim/Vundle.vim.git ~/.vim/bundle/Vundle.vim
# Run at Vim's command line: `:PluginInstall`

# VIM: install colorscheme.
mkdir -p ~/.vim/colors &&  cd ~/.vim/colors && wget https://raw.githubusercontent.com/arcticicestudio/nord-vim/develop/colors/nord.vim

# Create symlinks
cd
mklink .vimrc dotfiles\.vimrc
mklink .bash_profile dotfiles\.bash_profile
mklink .config\git\git-prompt.sh dotfiles\scripts\git-prompt.sh
mklink .gitignore dotfiles\.gitignore
mklink .gitattributes dotfiles\.gitattributes
mklink .gitconfig dotfiles\.gitconfig
