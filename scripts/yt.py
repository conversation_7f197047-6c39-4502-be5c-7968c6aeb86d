import sys
import re
from urllib.parse import urlparse, parse_qs
from youtube_transcript_api import YouTubeTranscriptApi


def extract_video_id(url_or_id):
    """
    Extract video ID from YouTube URL or return the ID if it's already a video ID.

    Supports various YouTube URL formats:
    - https://www.youtube.com/watch?v=VIDEO_ID
    - https://youtu.be/VIDEO_ID
    - https://m.youtube.com/watch?v=VIDEO_ID
    - https://youtube.com/watch?v=VIDEO_ID
    - VIDEO_ID (direct video ID)
    """
    # If it's already a video ID (11 characters, alphanumeric and some special chars)
    if re.match(r'^[a-zA-Z0-9_-]{11}$', url_or_id):
        return url_or_id

    # Parse different YouTube URL formats
    if 'youtu.be/' in url_or_id:
        # Format: https://youtu.be/VIDEO_ID
        return url_or_id.split('youtu.be/')[-1].split('?')[0]

    if 'youtube.com/watch' in url_or_id:
        # Format: https://www.youtube.com/watch?v=VIDEO_ID
        parsed_url = urlparse(url_or_id)
        query_params = parse_qs(parsed_url.query)
        if 'v' in query_params:
            return query_params['v'][0]

    # If we can't extract it, assume it's already a video ID
    return url_or_id


if len(sys.argv) < 2:
    print("Usage: python yt.py <YouTube_URL_or_Video_ID>")
    sys.exit(1)

url_or_id = sys.argv[1]  # Lấy URL hoặc video_id từ dòng lệnh
video_id = extract_video_id(url_or_id)

try:
    transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['vi'])  # Đổi 'vi' thành mã ngôn ngữ phù hợp
    print(" ".join([text['text'] for text in transcript]))
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1)

