#!/bin/bash

# Install Homebrew, it already has git.
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Apps
brew install --cask imageoptim appcleaner
brew install --cask microsoft-word microsoft-excel
brew install --cask stats espanso

# Dev tools
brew install svn node composer wp-cli php imagemagick
brew install --cask firefox brave-browser
brew install --cask visual-studio-code
brew install --cask poedit
brew install --cask dbngin sequel-ace mailhog

# Terminal
brew install bat exa

# Install Laravel valet, WordPress coding standards and vendor cleaner.
composer global require laravel/valet dealerdirect/phpcodesniffer-composer-installer wp-coding-standards/wpcs:dev-develop phpcompatibility/phpcompatibility-wp liborm85/composer-vendor-cleaner

curl -O https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts
sudo mv hosts /etc/

# Setup SSH keys
echo "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDELxBtxNhNTUPmRjUB6L/6z6D+uhOK02Fjeubm/X7oWWlKNtfNt/Mq0t3O8uDNFpN5V1YqB/qix7XoTkkBFNh3v2vgYag1xJMf78jLp3Mu2H0WzoBKGkIMfhoWLTG2NVHCHwL9Jz/9WE+jwLugevDoa1zC2QJ6VwzOi8yeNmv2HfA0LQRJHYGHRzWeNXqqudeXCBzRvdFKaPtzyVz+P3LiyjiP953PFtQNDs4L5lSyf70bwSaYRima5lKJ9Ar3DLksCPxoSl+TSOigH05xZJB80slC1XpjYo1tJn2zIz3S1cVsGJD/thButi5tXas8JjQMuNm+5oIfrznVhvsdODHv anhtran@DESKTOP-AHO3OAA" > ~/.ssh/id_rsa.pub
echo "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" > ~/.ssh/id_rsa
chmod 400 ~/.ssh/id_rsa
chmod 400 ~/.ssh/id_rsa.pub

# Show hidden files.
defaults write com.apple.finder AppleShowAllFiles TRUE;killall Finder

