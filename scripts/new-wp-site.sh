#!/bin/bash

createDb() {
	echo -e "\n# Creating database"
	mysql -h 127.0.0.1 -u root -p"root" -s -e "CREATE DATABASE $db_name;"
}

installWp() {
	echo -e "\n# Installing WordPress"

	rm -rf $path
	mkdir $path
	cd $path

	echo "  - Downloading WordPress"
	wp core download --skip-content --quiet

	echo "  - Creating symlink to wp-content..."
	rm -rf wp-content
	ln -s ~/sites/wp/wp-content wp-content

	echo "  - Updating wp-config.php file"
	mv wp-config-sample.php wp-config.php
	sed -i '' "s/database_name_here/$db_name/" wp-config.php
	sed -i '' "s/username_here/root/" wp-config.php
	sed -i '' "s/password_here/root/" wp-config.php
	sed -i '' "s/localhost/127.0.0.1/" wp-config.php

	echo "  - Installing WordPress"
	wp core install --url="$domain" --title="$domain" --admin_user=admin --admin_password=admin --admin_email="admin@$domain"

	echo -e "\n\n# WordPress admin account information:"
	echo "  - Admin URL: http://$domain/wp-admin/"
	echo "  - Username:  admin"
	echo "  - Password:  admin"

	echo -e "\nDONE\n"
}


# Setup variables
domain=$1.test
db_name=$1
path="/Users/<USER>/sites/$1"

# Run
createDb
installWp
